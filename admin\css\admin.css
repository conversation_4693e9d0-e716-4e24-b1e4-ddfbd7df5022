/* Admin Panel Styles */
:root {
    --admin-primary: #2c3e50;
    --admin-secondary: #3498db;
    --admin-accent: #e74c3c;
    --admin-success: #27ae60;
    --admin-warning: #f39c12;
    --admin-light: #ecf0f1;
    --admin-dark: #34495e;
    --admin-gray: #7f8c8d;
    --admin-white: #ffffff;
    --admin-transition: all 0.3s ease;
    --admin-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --admin-radius: 6px;
    --sidebar-width: 260px;
    --topbar-height: 70px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', sans-serif;
    background-color: var(--admin-light);
    color: var(--admin-dark);
    line-height: 1.6;
}

/* Layout */
.admin-wrapper {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: var(--admin-primary);
    color: var(--admin-white);
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: var(--admin-transition);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar .logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--admin-white);
}

.sidebar .logo i {
    font-size: 1.5rem;
    color: #f4a261;
}

.sidebar-nav ul {
    list-style: none;
    padding: 20px 0;
}

.sidebar-nav li {
    margin-bottom: 5px;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: var(--admin-transition);
    border-left: 3px solid transparent;
}

.sidebar-nav a:hover,
.sidebar-nav li.active a {
    background: rgba(255,255,255,0.1);
    color: var(--admin-white);
    border-left-color: #f4a261;
}

.sidebar-nav i {
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    display: flex;
    flex-direction: column;
}

/* Topbar */
.topbar {
    height: var(--topbar-height);
    background: var(--admin-white);
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    box-shadow: var(--admin-shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--admin-gray);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
}

.sidebar-toggle:hover {
    background: var(--admin-light);
    color: var(--admin-dark);
}

.topbar h1 {
    font-size: 1.5rem;
    color: var(--admin-dark);
    font-weight: 600;
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
}

.dropdown {
    position: relative;
}

.dropdown-toggle {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--admin-gray);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: var(--admin-transition);
}

.dropdown-toggle:hover {
    background: var(--admin-light);
    color: var(--admin-dark);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--admin-white);
    border: 1px solid #e9ecef;
    border-radius: var(--admin-radius);
    box-shadow: var(--admin-shadow);
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--admin-transition);
    z-index: 1000;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    color: var(--admin-dark);
    text-decoration: none;
    transition: var(--admin-transition);
}

.dropdown-menu a:hover {
    background: var(--admin-light);
}

.dropdown-divider {
    height: 1px;
    background: #e9ecef;
    margin: 5px 0;
}

/* Content */
.content {
    padding: 30px;
    flex: 1;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--admin-white);
    border-radius: var(--admin-radius);
    padding: 25px;
    box-shadow: var(--admin-shadow);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--admin-transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--admin-white);
}

.stat-card:nth-child(1) .stat-icon {
    background: var(--admin-secondary);
}

.stat-card:nth-child(2) .stat-icon {
    background: var(--admin-success);
}

.stat-card:nth-child(3) .stat-icon {
    background: var(--admin-warning);
}

.stat-card:nth-child(4) .stat-icon {
    background: var(--admin-accent);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--admin-dark);
    margin-bottom: 5px;
}

.stat-content p {
    color: var(--admin-gray);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* Dashboard Cards */
.dashboard-card {
    background: var(--admin-white);
    border-radius: var(--admin-radius);
    box-shadow: var(--admin-shadow);
    overflow: hidden;
}

.card-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: 1.1rem;
    color: var(--admin-dark);
    font-weight: 600;
}

.card-content {
    padding: 25px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: var(--admin-secondary);
    color: var(--admin-white);
    text-decoration: none;
    border-radius: var(--admin-radius);
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--admin-transition);
    border: none;
    cursor: pointer;
}

.btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* Project List */
.project-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.project-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: var(--admin-light);
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
}

.project-item:hover {
    background: #d5dbdb;
}

.project-info h4 {
    font-size: 1rem;
    color: var(--admin-dark);
    margin-bottom: 5px;
}

.project-info p {
    font-size: 0.85rem;
    color: var(--admin-gray);
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-progress {
    background: #e8f4fd;
    color: var(--admin-secondary);
}

.status-planning {
    background: #fef9e7;
    color: var(--admin-warning);
}

.status-completed {
    background: #eafaf1;
    color: var(--admin-success);
}

/* Message List */
.message-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    background: var(--admin-light);
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
}

.message-item:hover {
    background: #d5dbdb;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--admin-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--admin-white);
    flex-shrink: 0;
}

.message-content h4 {
    font-size: 0.95rem;
    color: var(--admin-dark);
    margin-bottom: 5px;
}

.message-content p {
    font-size: 0.85rem;
    color: var(--admin-gray);
    margin-bottom: 5px;
    line-height: 1.4;
}

.message-content small {
    font-size: 0.75rem;
    color: var(--admin-gray);
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 25px 20px;
    background: var(--admin-light);
    border-radius: var(--admin-radius);
    text-decoration: none;
    color: var(--admin-dark);
    transition: var(--admin-transition);
    text-align: center;
}

.action-btn:hover {
    background: #d5dbdb;
    transform: translateY(-2px);
}

.action-btn i {
    font-size: 2rem;
    color: var(--admin-secondary);
}

.action-btn span {
    font-weight: 500;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .topbar {
        padding: 0 15px;
    }
    
    .content {
        padding: 20px 15px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}
