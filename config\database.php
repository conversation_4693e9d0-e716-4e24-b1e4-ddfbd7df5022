<?php
/**
 * Database Configuration for BuildPro Construction Platform
 * 
 * This file contains the database connection settings and helper functions
 * for the BuildPro Construction management system.
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'buildpro_construction');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Database connection class
class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    private $pdo;

    /**
     * Get database connection
     */
    public function getConnection() {
        $this->pdo = null;

        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }

        return $this->pdo;
    }
}

/**
 * Database initialization and table creation
 */
function initializeDatabase() {
    $database = new Database();
    $pdo = $database->getConnection();

    if (!$pdo) {
        die("Database connection failed");
    }

    // Create projects table
    $sql = "CREATE TABLE IF NOT EXISTS projects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        category ENUM('residential', 'commercial', 'industrial') NOT NULL,
        status ENUM('planning', 'in_progress', 'completed', 'on_hold') DEFAULT 'planning',
        start_date DATE,
        end_date DATE,
        budget DECIMAL(12,2),
        client_name VARCHAR(255),
        client_email VARCHAR(255),
        client_phone VARCHAR(50),
        image_url VARCHAR(500),
        gallery_images JSON,
        progress_percentage INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);

    // Create services table
    $sql = "CREATE TABLE IF NOT EXISTS services (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        icon VARCHAR(100),
        features JSON,
        price_range VARCHAR(100),
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);

    // Create gallery table
    $sql = "CREATE TABLE IF NOT EXISTS gallery (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255),
        description TEXT,
        image_url VARCHAR(500) NOT NULL,
        category VARCHAR(100),
        project_id INT,
        is_featured BOOLEAN DEFAULT FALSE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
    )";
    $pdo->exec($sql);

    // Create testimonials table
    $sql = "CREATE TABLE IF NOT EXISTS testimonials (
        id INT AUTO_INCREMENT PRIMARY KEY,
        client_name VARCHAR(255) NOT NULL,
        client_position VARCHAR(255),
        client_company VARCHAR(255),
        client_image VARCHAR(500),
        testimonial TEXT NOT NULL,
        rating INT DEFAULT 5,
        project_id INT,
        is_featured BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
    )";
    $pdo->exec($sql);

    // Create contact_messages table
    $sql = "CREATE TABLE IF NOT EXISTS contact_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        phone VARCHAR(50),
        subject VARCHAR(255),
        service VARCHAR(100),
        message TEXT NOT NULL,
        status ENUM('new', 'read', 'replied', 'archived') DEFAULT 'new',
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);

    // Create newsletter_subscribers table
    $sql = "CREATE TABLE IF NOT EXISTS newsletter_subscribers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        status ENUM('active', 'unsubscribed') DEFAULT 'active',
        subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        unsubscribed_at TIMESTAMP NULL
    )";
    $pdo->exec($sql);

    // Create admin_users table
    $sql = "CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(255),
        role ENUM('admin', 'manager', 'editor') DEFAULT 'editor',
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);

    // Create site_settings table
    $sql = "CREATE TABLE IF NOT EXISTS site_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type ENUM('text', 'textarea', 'number', 'boolean', 'json') DEFAULT 'text',
        description TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);

    // Insert default admin user (password: buildpro2023)
    $sql = "INSERT IGNORE INTO admin_users (username, email, password_hash, full_name, role) 
            VALUES ('admin', '<EMAIL>', ?, 'System Administrator', 'admin')";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([password_hash('buildpro2023', PASSWORD_DEFAULT)]);

    // Insert default site settings
    $defaultSettings = [
        ['company_name', 'BuildPro Construction', 'text', 'Company name displayed on the website'],
        ['company_email', '<EMAIL>', 'text', 'Main company email address'],
        ['company_phone', '+****************', 'text', 'Main company phone number'],
        ['company_address', '123 Construction Avenue, Building City, BC 12345', 'textarea', 'Company physical address'],
        ['site_title', 'BuildPro Construction | Premium Construction Services', 'text', 'Website title for SEO'],
        ['site_description', 'BuildPro Construction - Premium construction services with 20+ years of experience. Residential, commercial, and industrial construction solutions.', 'textarea', 'Website description for SEO'],
        ['social_facebook', '#', 'text', 'Facebook page URL'],
        ['social_twitter', '#', 'text', 'Twitter profile URL'],
        ['social_instagram', '#', 'text', 'Instagram profile URL'],
        ['social_linkedin', '#', 'text', 'LinkedIn profile URL'],
        ['social_youtube', '#', 'text', 'YouTube channel URL'],
    ];

    foreach ($defaultSettings as $setting) {
        $sql = "INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) 
                VALUES (?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($setting);
    }

    // Insert sample data
    insertSampleData($pdo);

    return true;
}

/**
 * Insert sample data for demonstration
 */
function insertSampleData($pdo) {
    // Sample projects
    $projects = [
        [
            'Metropolitan Office Complex',
            'Modern office building with sustainable design features and state-of-the-art facilities.',
            'commercial',
            'in_progress',
            '2023-06-01',
            '2024-03-15',
            2500000.00,
            'Tech Innovations Inc.',
            '<EMAIL>',
            '+1 (555) 234-5678',
            'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-4.0.3',
            75
        ],
        [
            'Lakeside Luxury Residence',
            'Custom waterfront home with panoramic views and premium finishes.',
            'residential',
            'planning',
            '2024-01-15',
            '2024-12-20',
            850000.00,
            'John & Sarah Smith',
            '<EMAIL>',
            '+1 (555) 345-6789',
            'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3',
            25
        ],
        [
            'Advanced Manufacturing Facility',
            'State-of-the-art production plant with automation systems and safety features.',
            'industrial',
            'completed',
            '2022-08-01',
            '2023-05-30',
            4200000.00,
            'Manufacturing Corp',
            '<EMAIL>',
            '+****************',
            'https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3',
            100
        ]
    ];

    foreach ($projects as $project) {
        $sql = "INSERT IGNORE INTO projects (title, description, category, status, start_date, end_date, budget, client_name, client_email, client_phone, image_url, progress_percentage) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($project);
    }

    // Sample services
    $services = [
        [
            'Residential Construction',
            'Custom home building, renovations, additions, and remodeling services tailored to your unique vision and lifestyle.',
            'fas fa-home',
            '["Custom Home Design", "Home Renovations", "Kitchen & Bath Remodeling", "Home Additions"]',
            '$50K - $2M+',
            1
        ],
        [
            'Commercial Construction',
            'Office buildings, retail spaces, restaurants, and other commercial properties built to industry standards.',
            'fas fa-building',
            '["Office Buildings", "Retail Spaces", "Restaurants & Hotels", "Medical Facilities"]',
            '$100K - $10M+',
            2
        ],
        [
            'Industrial Construction',
            'Warehouses, factories, and specialized industrial facilities designed for efficiency and safety.',
            'fas fa-industry',
            '["Manufacturing Plants", "Warehouses", "Distribution Centers", "Processing Facilities"]',
            '$500K - $50M+',
            3
        ]
    ];

    foreach ($services as $service) {
        $sql = "INSERT IGNORE INTO services (name, description, icon, features, price_range, sort_order) 
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($service);
    }

    // Sample testimonials
    $testimonials = [
        [
            'John Smith',
            'CEO',
            'Tech Innovations Inc.',
            'https://randomuser.me/api/portraits/men/32.jpg',
            'BuildPro delivered our commercial project ahead of schedule and under budget. Their attention to detail and professional management made the entire process smooth and stress-free.',
            5,
            1
        ],
        [
            'Sarah Johnson',
            'Homeowner',
            '',
            'https://randomuser.me/api/portraits/women/44.jpg',
            'Outstanding quality and craftsmanship! Our new home exceeded all expectations. The team was professional, communicative, and delivered exactly what they promised.',
            5,
            2
        ],
        [
            'Michael Brown',
            'Operations Manager',
            'Global Corp',
            'https://randomuser.me/api/portraits/men/56.jpg',
            'Exceptional service from start to finish. BuildPro transformed our outdated office space into a modern, functional workplace that our employees love.',
            5,
            null
        ]
    ];

    foreach ($testimonials as $testimonial) {
        $sql = "INSERT IGNORE INTO testimonials (client_name, client_position, client_company, client_image, testimonial, rating, project_id) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($testimonial);
    }
}

// Initialize database if this file is run directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    if (initializeDatabase()) {
        echo "Database initialized successfully!";
    } else {
        echo "Database initialization failed!";
    }
}
?>
