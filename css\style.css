/* Global Styles */
:root {
    --primary: #0d3b66;
    --secondary: #f4a261;
    --accent: #e76f51;
    --light: #f8f9fa;
    --dark: #212529;
    --gray: #6c757d;
    --white: #ffffff;
    --transition: all 0.3s ease;
    --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.15);
    --radius: 8px;
    --radius-lg: 12px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Open Sans', sans-serif;
    line-height: 1.6;
    color: var(--dark);
    background-color: var(--white);
    overflow-x: hidden;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: var(--primary);
}

p {
    margin-bottom: 1rem;
    color: var(--gray);
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--white);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loader {
    text-align: center;
}

.construction-loader i {
    font-size: 3rem;
    color: var(--secondary);
    animation: bounce 1s infinite;
}

.construction-loader p {
    margin-top: 1rem;
    font-weight: 600;
    color: var(--primary);
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-10px);
    }

    60% {
        transform: translateY(-5px);
    }
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 12px 28px;
    background-color: var(--secondary);
    color: var(--white);
    text-decoration: none;
    border-radius: var(--radius);
    font-weight: 600;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    background-color: var(--accent);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.btn-primary {
    background-color: var(--primary);
}

.btn-primary:hover {
    background-color: var(--secondary);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--secondary);
    color: var(--secondary);
}

.btn-outline:hover {
    background: var(--secondary);
    color: var(--white);
}

.btn-admin {
    background: var(--primary);
    font-size: 14px;
    padding: 8px 16px;
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
}

.section-title h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.section-title p {
    font-size: 1.1rem;
    color: var(--gray);
}

.section-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: var(--secondary);
    margin: 20px auto 0;
    border-radius: 2px;
}

/* Header Styles */
header {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 1000;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.header-scrolled {
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
}

.logo {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo i {
    color: var(--secondary);
}

.logo span {
    color: var(--secondary);
}

.nav-links {
    display: flex;
    list-style: none;
    align-items: center;
}

.nav-links li {
    margin-left: 30px;
}

.nav-links a {
    text-decoration: none;
    color: var(--dark);
    font-weight: 600;
    font-size: 16px;
    transition: var(--transition);
    position: relative;
}

.nav-links a:not(.btn)::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--secondary);
    transition: var(--transition);
}

.nav-links a:not(.btn):hover::after {
    width: 100%;
}

.nav-links a:not(.btn):hover {
    color: var(--secondary);
}

.mobile-toggle {
    display: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--primary);
}

/* Hero Section */
.hero {
    height: 100vh;
    min-height: 700px;
    position: relative;
    overflow: hidden;
}

.hero-slider {
    position: relative;
    width: 100%;
    height: 100%;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.hero-slide.active {
    opacity: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(13, 59, 102, 0.8), rgba(13, 59, 102, 0.6));
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--white);
    max-width: 800px;
    margin: 0 auto;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    color: var(--white);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-content p {
    font-size: 1.25rem;
    margin-bottom: 30px;
    color: rgba(255, 255, 255, 0.9);
}

.hero-btns {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Hero Navigation */
.hero-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
}

.hero-prev {
    left: 30px;
}

.hero-next {
    right: 30px;
}

.hero-prev,
.hero-next {
    position: absolute;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--white);
    font-size: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.hero-prev:hover,
.hero-next:hover {
    background: var(--secondary);
    transform: scale(1.1);
}

/* Hero Indicators */
.hero-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 3;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: var(--transition);
}

.indicator.active {
    background: var(--secondary);
    transform: scale(1.2);
}

/* About Section */
.about {
    background-color: var(--light);
}

.about-content {
    display: flex;
    align-items: center;
    gap: 50px;
    margin-bottom: 60px;
}

.about-text {
    flex: 1;
}

.about-text h3 {
    font-size: 2rem;
    margin-bottom: 20px;
    color: var(--primary);
}

.about-text p {
    font-size: 1.1rem;
    margin-bottom: 20px;
}

.features-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 30px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.feature-item i {
    color: var(--secondary);
    font-size: 18px;
}

.feature-item span {
    font-weight: 600;
    color: var(--dark);
}

.about-image {
    flex: 1;
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.about-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: var(--transition);
}

.about-image:hover img {
    transform: scale(1.05);
}

.experience-badge {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: var(--secondary);
    color: var(--white);
    padding: 20px;
    border-radius: var(--radius);
    text-align: center;
    box-shadow: var(--shadow);
}

.experience-badge .years {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.experience-badge .text {
    font-size: 0.9rem;
    font-weight: 600;
}

/* Statistics */
.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
}

.stat-item {
    text-align: center;
    padding: 30px 20px;
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--secondary);
    margin-bottom: 10px;
    font-family: 'Montserrat', sans-serif;
}

.stat-text {
    font-weight: 600;
    color: var(--primary);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

/* Services Section */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.service-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.service-image {
    height: 250px;
    overflow: hidden;
    position: relative;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.service-card:hover .service-image img {
    transform: scale(1.1);
}

.service-content {
    padding: 30px;
}

.service-icon {
    font-size: 3rem;
    color: var(--secondary);
    margin-bottom: 20px;
}

.service-content h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--primary);
}

.service-content p {
    margin-bottom: 20px;
    line-height: 1.7;
}

.service-features {
    list-style: none;
    margin-bottom: 25px;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    color: var(--dark);
}

.service-features i {
    color: var(--secondary);
    font-size: 14px;
}

/* Animations */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-content h1 {
        font-size: 2.8rem;
    }

    .about-content {
        flex-direction: column;
    }

    .features-list {
        grid-template-columns: 1fr;
    }

    .hero-btns {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 768px) {
    .mobile-toggle {
        display: block;
    }

    .nav-links {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: var(--white);
        flex-direction: column;
        padding: 20px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
        transition: var(--transition);
    }

    .nav-links.active {
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    }

    .nav-links li {
        margin: 15px 0;
    }

    .hero-content h1 {
        font-size: 2.2rem;
    }

    section {
        padding: 60px 0;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .hero-nav {
        display: none;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .hero {
        min-height: 600px;
    }

    .hero-content h1 {
        font-size: 1.8rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 14px;
    }

    .container {
        padding: 0 10px;
    }

    .stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .stat-number {
        font-size: 2rem;
    }
}

/* Projects Section */
.projects {
    background-color: var(--light);
}

.projects-filter {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 40px;
}

.filter-btn {
    background: transparent;
    border: 2px solid var(--gray);
    padding: 10px 25px;
    margin: 5px;
    cursor: pointer;
    font-weight: 600;
    color: var(--gray);
    transition: var(--transition);
    border-radius: 30px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--secondary);
    border-color: var(--secondary);
    color: var(--white);
    transform: translateY(-2px);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
}

.project-card {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
    height: 300px;
    transition: var(--transition);
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.project-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(13, 59, 102, 0.85), rgba(231, 111, 81, 0.85));
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 30px;
    text-align: center;
    color: var(--white);
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-card:hover .project-image {
    transform: scale(1.1);
}

.project-overlay h3 {
    color: var(--white);
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.project-overlay p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 20px;
    line-height: 1.6;
}

/* Gallery Section */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.gallery-item {
    position: relative;
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    height: 250px;
    cursor: pointer;
    transition: var(--transition);
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(13, 59, 102, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-item:hover .gallery-image {
    transform: scale(1.1);
}

.gallery-overlay i {
    font-size: 2rem;
    color: var(--white);
}

/* Testimonials */
.testimonials {
    background-color: var(--light);
}

.testimonials-slider {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.testimonial {
    background: var(--white);
    padding: 40px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    text-align: center;
    margin: 20px;
    display: none;
    transition: var(--transition);
}

.testimonial.active {
    display: block;
    animation: fadeInUp 0.5s ease;
}

.client-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 20px;
    overflow: hidden;
    border: 3px solid var(--secondary);
}

.client-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.quote {
    font-size: 18px;
    font-style: italic;
    margin-bottom: 25px;
    color: var(--dark);
    line-height: 1.7;
    position: relative;
}

.quote::before {
    content: '"';
    font-size: 4rem;
    color: var(--secondary);
    position: absolute;
    top: -20px;
    left: -20px;
    font-family: serif;
}

.client-name {
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 5px;
}

.client-position {
    color: var(--gray);
    font-size: 14px;
    margin-bottom: 15px;
}

.rating {
    display: flex;
    justify-content: center;
    gap: 5px;
}

.rating i {
    color: #ffc107;
    font-size: 16px;
}

.testimonial-nav {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.testimonial-prev,
.testimonial-next {
    background: var(--secondary);
    border: none;
    color: var(--white);
    font-size: 18px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
}

.testimonial-prev:hover,
.testimonial-next:hover {
    background: var(--accent);
    transform: scale(1.1);
}

/* Contact Section */
.contact {
    background: linear-gradient(135deg, var(--primary), #1a5a8f);
    color: var(--white);
}

.contact .section-title {
    color: var(--white);
}

.contact .section-title::after {
    background: var(--secondary);
}

.contact-wrapper {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 50px;
    align-items: start;
}

.contact-info h3 {
    color: var(--secondary);
    margin-bottom: 30px;
    font-size: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius);
    backdrop-filter: blur(10px);
    transition: var(--transition);
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(10px);
}

.contact-icon {
    font-size: 24px;
    color: var(--secondary);
    margin-right: 20px;
    min-width: 30px;
    margin-top: 5px;
}

.contact-item h4 {
    color: var(--white);
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.contact-item p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    line-height: 1.6;
}

.contact-form {
    background: var(--white);
    padding: 40px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
}

.contact-form h3 {
    color: var(--primary);
    margin-bottom: 30px;
    font-size: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-control {
    width: 100%;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: var(--radius);
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
    transition: var(--transition);
    background: var(--white);
}

.form-control:focus {
    border-color: var(--secondary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(244, 162, 97, 0.2);
}

.form-control::placeholder {
    color: var(--gray);
}

select.form-control {
    cursor: pointer;
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

/* Footer */
footer {
    background: var(--dark);
    color: #adb5bd;
    padding: 60px 0 0;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-col h4 {
    color: var(--white);
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 15px;
    font-size: 1.2rem;
}

.footer-col h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--secondary);
    border-radius: 2px;
}

.footer-col h5 {
    color: var(--white);
    margin-bottom: 15px;
    margin-top: 25px;
    font-size: 1rem;
}

.footer-col p {
    line-height: 1.7;
    margin-bottom: 20px;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: #adb5bd;
    text-decoration: none;
    transition: var(--transition);
    display: inline-block;
    position: relative;
}

.footer-links a::before {
    content: '';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 2px;
    background: var(--secondary);
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--secondary);
    transform: translateX(15px);
}

.footer-links a:hover::before {
    width: 10px;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 25px;
}

.social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border-radius: 50%;
    transition: var(--transition);
    font-size: 18px;
}

.social-links a:hover {
    background: var(--secondary);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(244, 162, 97, 0.4);
}

.newsletter-form {
    margin-top: 20px;
}

.newsletter-form .form-group {
    margin-bottom: 15px;
}

.newsletter-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
}

.newsletter-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-form .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--secondary);
}

.btn-secondary {
    background: var(--secondary);
    border: none;
    padding: 10px 20px;
    font-size: 14px;
}

.certifications {
    margin-top: 25px;
}

.cert-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.cert-badge {
    background: var(--secondary);
    color: var(--white);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.copyright {
    text-align: center;
    padding: 25px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 14px;
    background: rgba(0, 0, 0, 0.3);
}

.copyright-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-legal {
    display: flex;
    gap: 20px;
}

.footer-legal a {
    color: #adb5bd;
    text-decoration: none;
    font-size: 14px;
    transition: var(--transition);
}

.footer-legal a:hover {
    color: var(--secondary);
}

/* Notification System */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    padding: 20px;
    max-width: 400px;
    z-index: 10000;
    transform: translateX(100%);
    transition: var(--transition);
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.notification-success {
    border-left: 4px solid #28a745;
}

.notification-success i {
    color: #28a745;
}

.notification-close {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    color: var(--gray);
    margin-left: auto;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: var(--secondary);
    color: var(--white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 18px;
    cursor: pointer;
    transition: var(--transition);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--accent);
    transform: translateY(-3px);
}

/* Additional Responsive Styles */
@media (max-width: 992px) {
    .contact-wrapper {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .projects-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .testimonial-nav {
        display: none;
    }
}

@media (max-width: 768px) {
    .projects-filter {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 10px;
    }

    .filter-btn {
        white-space: nowrap;
        flex-shrink: 0;
    }

    .copyright-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-legal {
        justify-content: center;
    }

    .social-links {
        justify-content: center;
    }

    .cert-badges {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .contact-wrapper {
        grid-template-columns: 1fr;
    }

    .contact-form {
        padding: 25px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }

    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* Print Styles */
@media print {

    .hero-nav,
    .hero-indicators,
    .back-to-top,
    .notification,
    header,
    .mobile-toggle {
        display: none !important;
    }

    .hero {
        height: auto;
        min-height: auto;
        page-break-after: always;
    }

    section {
        padding: 40px 0;
    }

    .container {
        max-width: none;
        padding: 0;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary: #000000;
        --secondary: #ff6600;
        --accent: #cc0000;
        --gray: #333333;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .hero-slide {
        transition: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1a1a1a;
        --light: #2d2d2d;
        --dark: #ffffff;
        --gray: #cccccc;
    }

    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }

    .service-card,
    .stat-item,
    .testimonial,
    .contact-form {
        background: #2d2d2d;
        color: #ffffff;
    }

    .form-control {
        background: #2d2d2d;
        border-color: #444444;
        color: #ffffff;
    }

    .form-control::placeholder {
        color: #888888;
    }
}

/* Lightbox Styles */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.lightbox.show {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    animation: lightboxZoom 0.3s ease;
}

.lightbox-content img {
    width: 100%;
    height: auto;
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: -40px;
    background: var(--white);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 18px;
    cursor: pointer;
    transition: var(--transition);
    color: var(--dark);
}

.lightbox-close:hover {
    background: var(--secondary);
    color: var(--white);
    transform: scale(1.1);
}

@keyframes lightboxZoom {
    from {
        transform: scale(0.8);
        opacity: 0;
    }

    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles */
*:focus {
    outline: 2px solid var(--secondary);
    outline-offset: 2px;
}

.btn:focus,
.form-control:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(244, 162, 97, 0.3);
}

/* Skip Link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius);
    z-index: 10000;
    transition: var(--transition);
}

.skip-link:focus {
    top: 6px;
}