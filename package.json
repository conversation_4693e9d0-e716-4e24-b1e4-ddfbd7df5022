{"name": "buildpro-construction-platform", "version": "1.0.0", "description": "Comprehensive construction company web platform with responsive design, admin panel, and mobile app integration", "main": "index.php", "scripts": {"dev": "php -S localhost:8000", "build": "npm run build-css && npm run build-js", "build-css": "postcss src/css/main.css -o public/css/style.css", "build-js": "webpack --mode production", "watch": "webpack --mode development --watch", "test": "phpunit tests/", "mobile": "cd mobile-app && npm start"}, "keywords": ["construction", "web-platform", "responsive-design", "admin-panel", "mobile-app", "php", "mysql", "react-native"], "author": "BuildPro Construction", "license": "MIT", "devDependencies": {"webpack": "^5.88.0", "webpack-cli": "^5.1.0", "postcss": "^8.4.24", "postcss-cli": "^10.1.0", "autoprefixer": "^10.4.14", "cssnano": "^6.0.1"}, "dependencies": {"bootstrap": "^5.3.0", "jquery": "^3.7.0", "aos": "^2.3.4", "swiper": "^10.0.4"}, "engines": {"node": ">=16.0.0", "php": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/buildpro/construction-platform.git"}, "bugs": {"url": "https://github.com/buildpro/construction-platform/issues"}, "homepage": "https://buildproconstruction.com"}